我希望用一个设计一个现代的可托管在vercel的网页程序，用于多用户的日常饮食的热量分析和饮食分析的记录。因此需要用到数据库。
（全部用北京时间）
主要功能分为以下步骤：
1，饮食分析和记录。用户输入大概的吃的食物和大概的量，用大模型的function call 提取json格式的食物中单独的各种食材，和估计大概数量（以g计算），以及根据输入，提取出用户输入内容包含的菜品和主食和辅食、饮料的名称（可能有多种，中文表示）。用户输入可以是自然语言或者直接拍照上传图片，使用到的大模型是支持多模态的，因此可以直接处理。处理完之后弹出分析结果，将各种原料和数量弹出，并提供识别的时间，和根据时间判断为早中晚餐。由于识别可能不够准确，因此弹出的识别结果的每个参数都可以点击修改，包括各种原料名称，数量，早中晚餐是根据时间判断的，除此之外还有一个加餐，下拉框中提供选择，时间也可以修改，菜品和主食和辅食、饮料单独写入到数据库的菜品库中，注意合并去重，用于单独记录菜品饮料等种类。提供确定按钮，确定之后记录进入数据库。
要将用户原始的输入记录进入数据库，注意，如果输入的是图片，要调用图床（参考image.md），获取永久的缩略图的地址，写入数据库。
2，热量分析和记录。用上一步得到的各个原料和数量，调用大模型，用prompt限定模型，使得大模型根据输入的食材和数量，分析出这一餐总的热量，和简短的点评。将结果写入数据库，并从中提取出总的热量，单独的写入数据库。将大模型给出的最终结果展示出来。
3，提供一个管理员账号，用户名wrhsd，密码a123456, 写入环境变量，在管理员页面可以增删用户，为每个用户重置密码，绑定授权码功能。
可以设置大模型的api的 baseURL，默认为 https://api.openai.com 和apikey。 是标准的openai格式，会话的具体调用路径为 baseURL+/授权码/+v1/chat/completions，模型列表的路径为 baseURL+/授权码/+v1/models
4，数据库记录的内容比较复杂，包括每个用户每次输入的原始输入（文字或者图片缩率图外链），原料和数量，菜品主食饮料等的名称，模型热量分析结果，总热量，餐次，时间等信息。
以及一个总的去重的菜品主食饮料等名称库。
5，用户页面提供时间轴的历史查看功能，可以通过数据库看到每餐的信息，并提供删除功能，以及一个设置页面，可以设置的选项包括每一步想要使用的模型名称，下拉框中的候选项有baseURL+/授权码/+v1/models 提供。

我想到的就这些，比较复杂，我希望你设计一个现代的，美观的，简洁的，高效的，pc端和手机端通用的程序。
比较复杂，你首先详细的设计一个技术路线图，规划好每一步，然后分步执行，我相信你的实力，加油

